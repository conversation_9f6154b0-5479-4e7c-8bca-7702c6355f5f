<?php
/**
 * Auto Extractor DAZN - Extrae automáticamente URLs MPD desde páginas de eventos
 * Simula F12 para obtener las URLs de stream directamente
 */

require_once 'converter.php';

class DaznAutoExtractor {
    
    private $cookies = [];
    private $headers = [
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language: es-ES,es;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate, br',
        'DNT: 1',
        'Connection: keep-alive',
        'Upgrade-Insecure-Requests: 1',
        'Sec-Fetch-Dest: document',
        'Sec-Fetch-Mode: navigate',
        'Sec-Fetch-Site: none',
        'Cache-Control: max-age=0'
    ];
    
    /**
     * Extrae Stream ID desde URL de evento de DAZN
     */
    public function extractFromEventUrl($eventUrl) {
        $this->log("🔍 Analizando URL del evento: $eventUrl");
        
        // Paso 1: Obtener la página del evento
        $pageContent = $this->makeRequest($eventUrl);
        if (!$pageContent) {
            return ['success' => false, 'error' => 'No se pudo cargar la página del evento'];
        }
        
        // Paso 2: Buscar URLs MPD en el contenido de la página
        $mpdUrls = $this->findMpdUrls($pageContent);
        
        if (!empty($mpdUrls)) {
            $this->log("✅ URLs MPD encontradas en la página: " . count($mpdUrls));
            return $this->processMpdUrls($mpdUrls);
        }
        
        // Paso 3: Buscar scripts y APIs que puedan contener URLs
        $apiUrls = $this->findApiUrls($pageContent);
        
        foreach ($apiUrls as $apiUrl) {
            $this->log("🔍 Probando API: $apiUrl");
            $apiContent = $this->makeRequest($apiUrl);
            
            if ($apiContent) {
                $mpdUrls = $this->findMpdUrls($apiContent);
                if (!empty($mpdUrls)) {
                    $this->log("✅ URLs MPD encontradas en API");
                    return $this->processMpdUrls($mpdUrls);
                }
            }
        }
        
        // Paso 4: Buscar en JavaScript embebido
        $jsMpdUrls = $this->extractFromJavaScript($pageContent);
        if (!empty($jsMpdUrls)) {
            $this->log("✅ URLs MPD encontradas en JavaScript");
            return $this->processMpdUrls($jsMpdUrls);
        }
        
        return ['success' => false, 'error' => 'No se encontraron URLs MPD en la página'];
    }
    
    /**
     * Busca URLs MPD en el contenido
     */
    private function findMpdUrls($content) {
        $mpdUrls = [];
        
        // Patrones para encontrar URLs MPD
        $patterns = [
            '/https?:\/\/[^"\s]+\.mpd[^"\s]*/i',
            '/https?:\/\/[^"\s]*dazn[^"\s]*\.mpd[^"\s]*/i',
            '/https?:\/\/[^"\s]*cdn[^"\s]*\.mpd[^"\s]*/i',
            '/"(https?:\/\/[^"]*\.mpd[^"]*)"/i',
            '/"url"\s*:\s*"(https?:\/\/[^"]*\.mpd[^"]*)"/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                foreach ($matches[1] ?? $matches[0] as $url) {
                    $cleanUrl = html_entity_decode($url);
                    if (strpos($cleanUrl, '.mpd') !== false) {
                        $mpdUrls[] = $cleanUrl;
                    }
                }
            }
        }
        
        return array_unique($mpdUrls);
    }
    
    /**
     * Busca URLs de APIs que puedan contener streams
     */
    private function findApiUrls($content) {
        $apiUrls = [];
        
        // Patrones para APIs de DAZN
        $patterns = [
            '/https?:\/\/[^"\s]*isl\.dazn\.com[^"\s]*/i',
            '/https?:\/\/[^"\s]*api[^"\s]*dazn[^"\s]*/i',
            '/"(https?:\/\/[^"]*PlaybackScenarios[^"]*)"/i',
            '/"(https?:\/\/[^"]*misl\/v[0-9]+[^"]*)"/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                foreach ($matches[1] ?? $matches[0] as $url) {
                    $apiUrls[] = html_entity_decode($url);
                }
            }
        }
        
        return array_unique($apiUrls);
    }
    
    /**
     * Extrae URLs desde JavaScript embebido
     */
    private function extractFromJavaScript($content) {
        $mpdUrls = [];
        
        // Buscar bloques de JavaScript
        if (preg_match_all('/<script[^>]*>(.*?)<\/script>/is', $content, $scriptMatches)) {
            foreach ($scriptMatches[1] as $script) {
                // Buscar configuraciones de video
                if (preg_match('/videoSources|playbackUrl|streamUrl|manifestUrl/i', $script)) {
                    $urls = $this->findMpdUrls($script);
                    $mpdUrls = array_merge($mpdUrls, $urls);
                }
            }
        }
        
        return array_unique($mpdUrls);
    }
    
    /**
     * Procesa las URLs MPD encontradas
     */
    private function processMpdUrls($mpdUrls) {
        $results = [];
        
        foreach ($mpdUrls as $mpdUrl) {
            $this->log("🔄 Procesando: " . substr($mpdUrl, 0, 100) . "...");
            
            $result = SimpleConverter::process($mpdUrl);
            if ($result['success']) {
                $results[] = $result;
                $this->log("✅ Conversión exitosa para Stream ID: " . $result['stream_id']);
            }
        }
        
        if (!empty($results)) {
            return [
                'success' => true,
                'streams_found' => count($results),
                'streams' => $results,
                'primary_stream' => $results[0] // El primer stream encontrado
            ];
        }
        
        return ['success' => false, 'error' => 'No se pudieron procesar las URLs MPD encontradas'];
    }
    
    /**
     * Realiza petición HTTP
     */
    private function makeRequest($url) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => $this->headers,
            CURLOPT_COOKIEJAR => 'cookies_auto.txt',
            CURLOPT_COOKIEFILE => 'cookies_auto.txt',
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode >= 200 && $httpCode < 400 && $response) {
            return $response;
        }
        
        $this->log("❌ Error HTTP $httpCode para: $url");
        return null;
    }
    
    /**
     * Logging
     */
    private function log($message) {
        echo "[" . date('H:i:s') . "] " . $message . "\n";
    }
}

// API para uso web
if (isset($_POST['event_url']) || isset($_GET['event_url'])) {
    header('Content-Type: application/json');
    
    $eventUrl = $_POST['event_url'] ?? $_GET['event_url'] ?? '';
    
    if (empty($eventUrl)) {
        echo json_encode(['success' => false, 'error' => 'URL del evento requerida']);
        exit;
    }
    
    $extractor = new DaznAutoExtractor();
    $result = $extractor->extractFromEventUrl($eventUrl);
    
    echo json_encode($result);
    exit;
}

// Ejemplo de uso
if (isset($_GET['example'])) {
    $exampleUrl = 'https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg';
    
    echo "<h2>🎬 Auto Extractor DAZN - Ejemplo</h2>";
    echo "<h3>URL del Evento:</h3>";
    echo "<p>" . htmlspecialchars($exampleUrl) . "</p>";
    
    $extractor = new DaznAutoExtractor();
    $result = $extractor->extractFromEventUrl($exampleUrl);
    
    if ($result['success']) {
        echo "<h3>✅ Streams Encontrados: " . $result['streams_found'] . "</h3>";
        
        foreach ($result['streams'] as $i => $stream) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<h4>Stream " . ($i + 1) . "</h4>";
            echo "<p><strong>Stream ID:</strong> " . $stream['stream_id'] . "</p>";
            echo "<p><strong>Canal:</strong> " . ($stream['channel'] ?? 'N/A') . "</p>";
            echo "<p><strong>URL M3U8:</strong></p>";
            echo "<p style='background: #e8f5e8; padding: 10px; word-break: break-all;'>";
            echo "<strong>" . htmlspecialchars($stream['final_m3u8']) . "</strong>";
            echo "</p>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>❌ Error: " . $result['error'] . "</p>";
    }
    
    exit;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAZN Auto Extractor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .stream-item {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .final-url {
            background-color: #e8f5e8;
            border: 2px solid #4CAF50;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
        .loading {
            display: none;
            color: #007bff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🤖 DAZN Auto Extractor</h1>
    <p>Extrae automáticamente URLs M3U8 desde páginas de eventos de DAZN (simula F12)</p>
    
    <div class="container">
        <div class="form-group">
            <label for="event-url">URL del Evento de DAZN:</label>
            <input type="url" id="event-url" placeholder="https://www.dazn.com/es-PA/fixture/..." 
                   value="https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg">
        </div>
        <button onclick="extractStreams()">🔍 Extraer Streams</button>
        <button onclick="clearResults()">🗑️ Limpiar</button>
        
        <div class="loading" id="loading">🔄 Analizando página y extrayendo streams...</div>
        <div id="result" style="display:none;"></div>
    </div>
    
    <div class="container">
        <h3>📋 Cómo funciona:</h3>
        <ol>
            <li>Pega la URL completa del evento de DAZN</li>
            <li>El script simula un navegador y analiza la página</li>
            <li>Busca automáticamente URLs MPD en el HTML y JavaScript</li>
            <li>Convierte las URLs encontradas a formato M3U8</li>
            <li>Te entrega las URLs listas para usar</li>
        </ol>
        
        <p><strong>✅ Ventajas:</strong></p>
        <ul>
            <li>No necesitas abrir F12 manualmente</li>
            <li>Extrae múltiples streams si están disponibles</li>
            <li>Funciona con cualquier evento de DAZN</li>
            <li>Proceso completamente automatizado</li>
        </ul>
    </div>

    <script>
        function extractStreams() {
            const eventUrl = document.getElementById('event-url').value.trim();
            if (!eventUrl) {
                alert('Por favor ingresa la URL del evento');
                return;
            }
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            
            fetch('auto_extractor.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'event_url=' + encodeURIComponent(eventUrl)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                const resultDiv = document.getElementById('result');
                
                if (data.success) {
                    let html = `<h3>✅ Extracción Exitosa</h3>`;
                    html += `<p><strong>Streams encontrados:</strong> ${data.streams_found}</p>`;
                    
                    data.streams.forEach((stream, index) => {
                        html += `
                            <div class="stream-item">
                                <h4>🎬 Stream ${index + 1}</h4>
                                <p><strong>Stream ID:</strong> ${stream.stream_id}</p>
                                <p><strong>Canal:</strong> ${stream.channel || 'N/A'}</p>
                                <p><strong>Outlet:</strong> ${stream.outlet || 'N/A'}</p>
                                <p><strong>Idioma:</strong> ${stream.language || 'N/A'}</p>
                                <p><strong>URL M3U8:</strong></p>
                                <div class="final-url">${stream.final_m3u8}</div>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<h3>❌ Error</h3><p>${data.error}</p>`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.style.display = 'block';
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `<h3>❌ Error de Conexión</h3><p>${error.message}</p>`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            });
        }
        
        function clearResults() {
            document.getElementById('result').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
        }
    </script>
</body>
</html>
