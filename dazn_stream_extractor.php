<?php
/**
 * DAZN Stream Extractor - Script educativo para demostración técnica
 * IMPORTANTE: Solo para fines educativos. Respetar términos de servicio.
 */

class DaznStreamExtractor {
    
    private $email;
    private $password;
    private $cookies = [];
    private $headers = [
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept: application/json, text/plain, */*',
        'Accept-Language: es-ES,es;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate, br',
        'Connection: keep-alive',
        'Sec-Fetch-Dest: empty',
        'Sec-Fetch-Mode: cors',
        'Sec-Fetch-Site: same-origin'
    ];
    
    public function __construct($email, $password) {
        $this->email = $email;
        $this->password = $password;
    }
    
    /**
     * Extrae el ID del stream de una URL MPD
     */
    public function extractStreamId($mpdUrl) {
        // Patrón para extraer el ID del stream
        if (preg_match('/\/([a-z0-9]{26})\//', $mpdUrl, $matches)) {
            return $matches[1];
        }
        return null;
    }
    
    /**
     * Convierte URL MPD a M3U8
     */
    public function convertMpdToM3u8($mpdUrl) {
        $streamId = $this->extractStreamId($mpdUrl);
        if (!$streamId) {
            return null;
        }
        
        // Extraer parámetros de la URL original
        $urlParts = parse_url($mpdUrl);
        parse_str($urlParts['query'] ?? '', $params);
        
        // Construir URL M3U8
        $m3u8Url = "https://dcb-fl-live.dtcdn.dazn.com/{$streamId}/web/stream.m3u8";
        
        // Agregar parámetros si existen
        if (!empty($params)) {
            $m3u8Url .= '?' . http_build_query($params);
        }
        
        return $m3u8Url;
    }
    
    /**
     * Realiza login en DAZN
     */
    public function login() {
        $loginUrl = 'https://auth.dazn.com/api/v2/account/email/signin';
        
        $loginData = [
            'email' => $this->email,
            'password' => $this->password,
            'platform' => 'web'
        ];
        
        $response = $this->makeRequest($loginUrl, 'POST', $loginData);
        
        if ($response && isset($response['token'])) {
            $this->headers[] = 'Authorization: Bearer ' . $response['token'];
            return true;
        }
        
        return false;
    }
    
    /**
     * Obtiene eventos disponibles
     */
    public function getEvents() {
        $eventsUrl = 'https://isl.dazn.com/misl/v5/Catalog/pages/Home?isHome=true';
        
        $response = $this->makeRequest($eventsUrl);
        
        if ($response && isset($response['layout'])) {
            return $this->parseEvents($response['layout']);
        }
        
        return [];
    }
    
    /**
     * Parsea eventos de la respuesta
     */
    private function parseEvents($layout) {
        $events = [];
        
        foreach ($layout as $section) {
            if (isset($section['containers'])) {
                foreach ($section['containers'] as $container) {
                    if (isset($container['retrievalParameters']['sportId'])) {
                        $events[] = [
                            'id' => $container['id'] ?? 'unknown',
                            'title' => $container['metadata']['title'] ?? 'Sin título',
                            'sport' => $container['retrievalParameters']['sportId'] ?? 'unknown',
                            'isLive' => $container['metadata']['isLive'] ?? false
                        ];
                    }
                }
            }
        }
        
        return $events;
    }
    
    /**
     * Obtiene URL de stream para un evento específico
     */
    public function getStreamUrl($eventId) {
        $streamUrl = "https://isl.dazn.com/misl/v5/PlaybackScenarios/{$eventId}";
        
        $response = $this->makeRequest($streamUrl);
        
        if ($response && isset($response['videoSources'])) {
            foreach ($response['videoSources'] as $source) {
                if (isset($source['url']) && strpos($source['url'], '.mpd') !== false) {
                    return [
                        'mpd' => $source['url'],
                        'm3u8' => $this->convertMpdToM3u8($source['url']),
                        'streamId' => $this->extractStreamId($source['url'])
                    ];
                }
            }
        }
        
        return null;
    }
    
    /**
     * Realiza petición HTTP
     */
    private function makeRequest($url, $method = 'GET', $data = null) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HTTPHEADER => $this->headers,
            CURLOPT_COOKIEJAR => 'cookies.txt',
            CURLOPT_COOKIEFILE => 'cookies.txt',
            CURLOPT_TIMEOUT => 30
        ]);
        
        if ($method === 'POST' && $data) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            $this->headers[] = 'Content-Type: application/json';
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200 && $response) {
            return json_decode($response, true);
        }
        
        return null;
    }
    
    /**
     * Función de utilidad para logging
     */
    public function log($message) {
        echo "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
    }
}

// Ejemplo de uso
if (php_sapi_name() === 'cli' || isset($_GET['run'])) {
    
    // Credenciales
    $email = '<EMAIL>';
    $password = 'RVTbr9kXdZCaXnq';
    
    $extractor = new DaznStreamExtractor($email, $password);
    
    // Ejemplo 1: Convertir URL MPD a M3U8
    $mpdUrl = 'https://dcd-ak-livedazn.akamaized.net/1ks3vz4xf76pe14jyqg85ou3tv/tv25f/stream.mpd?channel=1637&outlet=dazn-row&plang=es&dazn-token=st%3D1750174536~exp%3D1750260936~acl%3D%2F*~id%3Dd0f04956b122~hmac%3D711bad5092b153e7903532028b81f6d564a82c3eb300633eb67e44629eadab0d';
    
    $extractor->log("URL MPD original: " . $mpdUrl);
    
    $streamId = $extractor->extractStreamId($mpdUrl);
    $extractor->log("Stream ID extraído: " . $streamId);
    
    $m3u8Url = $extractor->convertMpdToM3u8($mpdUrl);
    $extractor->log("URL M3U8 convertida: " . $m3u8Url);
    
    // Ejemplo 2: Intentar login y obtener eventos
    $extractor->log("Intentando login...");
    if ($extractor->login()) {
        $extractor->log("Login exitoso");
        
        $events = $extractor->getEvents();
        $extractor->log("Eventos encontrados: " . count($events));
        
        foreach (array_slice($events, 0, 5) as $event) {
            $extractor->log("Evento: " . $event['title'] . " (ID: " . $event['id'] . ")");
        }
    } else {
        $extractor->log("Error en login");
    }
    
    echo "\n--- Script completado ---\n";
}
?>
