<?php
/**
 * DAZN Ultimate Converter - Solución completa
 * Convierte URLs MPD a M3U8 Y extrae automáticamente desde URLs de eventos
 */

require_once 'converter.php';
require_once 'simple_auto.php';

// Determinar qué tipo de URL se proporcionó
function detectUrlType($url) {
    if (strpos($url, '.mpd') !== false) {
        return 'mpd';
    } elseif (strpos($url, 'dazn.com') !== false && strpos($url, 'fixture') !== false) {
        return 'event';
    } elseif (strpos($url, '.m3u8') !== false) {
        return 'm3u8';
    }
    return 'unknown';
}

// API unificada
if (isset($_POST['url']) || isset($_GET['url'])) {
    header('Content-Type: application/json');
    
    $url = $_POST['url'] ?? $_GET['url'] ?? '';
    
    if (empty($url)) {
        echo json_encode(['success' => false, 'error' => 'URL requerida']);
        exit;
    }
    
    $urlType = detectUrlType($url);
    
    switch ($urlType) {
        case 'mpd':
            // Convertir MPD directamente
            $result = SimpleConverter::process($url);
            break;
            
        case 'event':
            // Extraer desde URL de evento
            $extractor = new SimpleDaznExtractor();
            $result = $extractor->extractFromEventUrl($url);
            break;
            
        case 'm3u8':
            // Ya es M3U8, solo extraer información
            $result = [
                'success' => true,
                'message' => 'La URL ya está en formato M3U8',
                'final_m3u8' => $url,
                'stream_id' => 'N/A'
            ];
            break;
            
        default:
            $result = ['success' => false, 'error' => 'Tipo de URL no reconocido'];
            break;
    }
    
    echo json_encode($result);
    exit;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAZN Ultimate Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="url"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input[type="url"]:focus, textarea:focus {
            border-color: #667eea;
            outline: none;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }
        .success {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        .error {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }
        .stream-item {
            border: 2px solid #007bff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }
        .final-url {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 3px solid #4CAF50;
            padding: 15px;
            border-radius: 10px;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            font-weight: bold;
            margin: 10px 0;
        }
        .loading {
            display: none;
            text-align: center;
            color: #667eea;
            font-weight: bold;
            font-size: 16px;
            padding: 20px;
        }
        .url-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .type-mpd { background: #ffeaa7; color: #2d3436; }
        .type-event { background: #74b9ff; color: white; }
        .type-m3u8 { background: #00b894; color: white; }
        .examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .example-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .example-card h4 {
            margin-top: 0;
            color: #667eea;
        }
        .example-url {
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            word-break: break-all;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 DAZN Ultimate Converter</h1>
        <p>Convierte cualquier URL de DAZN a formato M3U8 listo para reproducir</p>
    </div>
    
    <div class="container">
        <div class="form-group">
            <label for="url-input">🔗 Pega cualquier URL de DAZN:</label>
            <textarea id="url-input" rows="3" placeholder="Pega aquí tu URL de DAZN (MPD, evento, o M3U8)..."></textarea>
            <div id="url-type" style="margin-top: 10px;"></div>
        </div>
        
        <div class="button-group">
            <button onclick="processUrl()">🚀 Procesar URL</button>
            <button onclick="loadMpdExample()">📝 Ejemplo MPD</button>
            <button onclick="loadEventExample()">🎯 Ejemplo Evento</button>
            <button onclick="clearAll()">🗑️ Limpiar</button>
        </div>
        
        <div class="loading" id="loading">
            <div>🔄 Procesando URL...</div>
            <div style="font-size: 14px; margin-top: 10px;">Esto puede tomar unos segundos</div>
        </div>
        
        <div id="result"></div>
    </div>
    
    <div class="container">
        <h3>📋 Tipos de URL soportados:</h3>
        <div class="examples">
            <div class="example-card">
                <h4>🎯 URL de Evento</h4>
                <p>Página completa del evento en DAZN</p>
                <div class="example-url">https://www.dazn.com/es-PA/fixture/ContentId:...</div>
                <p><strong>✅ Extrae automáticamente el stream</strong></p>
            </div>
            
            <div class="example-card">
                <h4>📺 URL MPD</h4>
                <p>URL directa del manifest (desde F12)</p>
                <div class="example-url">https://dch-ac-live.cdn.indazn.com/.../stream.mpd</div>
                <p><strong>✅ Convierte a M3U8</strong></p>
            </div>
            
            <div class="example-card">
                <h4>🎵 URL M3U8</h4>
                <p>URL ya en formato M3U8</p>
                <div class="example-url">https://dcb-fl-live.dtcdn.dazn.com/.../stream.m3u8</div>
                <p><strong>✅ Valida y muestra información</strong></p>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h3>🚀 Cómo usar:</h3>
        <ol>
            <li><strong>Opción 1:</strong> Pega la URL completa del evento de DAZN</li>
            <li><strong>Opción 2:</strong> Pega la URL MPD desde las herramientas de desarrollador (F12)</li>
            <li><strong>Opción 3:</strong> Pega una URL M3U8 existente para validar</li>
            <li>Haz clic en "Procesar URL"</li>
            <li>¡Copia la URL M3U8 resultante y úsala en tu reproductor!</li>
        </ol>
        
        <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h4 style="color: #0066cc; margin-top: 0;">💡 Ventajas del Ultimate Converter:</h4>
            <ul style="margin-bottom: 0;">
                <li>✅ Detecta automáticamente el tipo de URL</li>
                <li>✅ Funciona con URLs de eventos (no necesitas F12)</li>
                <li>✅ Convierte MPD a M3U8 instantáneamente</li>
                <li>✅ Interfaz moderna y fácil de usar</li>
                <li>✅ Compatible con todos los dominios de DAZN</li>
            </ul>
        </div>
    </div>

    <script>
        function detectUrlType(url) {
            if (url.includes('.mpd')) return 'mpd';
            if (url.includes('dazn.com') && url.includes('fixture')) return 'event';
            if (url.includes('.m3u8')) return 'm3u8';
            return 'unknown';
        }
        
        function updateUrlType() {
            const url = document.getElementById('url-input').value.trim();
            const typeDiv = document.getElementById('url-type');
            
            if (!url) {
                typeDiv.innerHTML = '';
                return;
            }
            
            const type = detectUrlType(url);
            let typeHtml = '';
            
            switch (type) {
                case 'mpd':
                    typeHtml = '<span class="url-type type-mpd">📺 URL MPD - Se convertirá a M3U8</span>';
                    break;
                case 'event':
                    typeHtml = '<span class="url-type type-event">🎯 URL de Evento - Se extraerá automáticamente</span>';
                    break;
                case 'm3u8':
                    typeHtml = '<span class="url-type type-m3u8">🎵 URL M3U8 - Se validará</span>';
                    break;
                default:
                    typeHtml = '<span class="url-type" style="background: #ff7675; color: white;">❓ Tipo de URL no reconocido</span>';
                    break;
            }
            
            typeDiv.innerHTML = typeHtml;
        }
        
        // Detectar tipo de URL mientras se escribe
        document.getElementById('url-input').addEventListener('input', updateUrlType);
        
        function processUrl() {
            const url = document.getElementById('url-input').value.trim();
            if (!url) {
                alert('Por favor ingresa una URL');
                return;
            }
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            
            fetch('dazn_ultimate.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'url=' + encodeURIComponent(url)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                const resultDiv = document.getElementById('result');
                
                if (data.success) {
                    let html = '<h3>✅ Procesamiento Exitoso</h3>';
                    
                    if (data.streams) {
                        // Resultado de extracción de evento
                        html += `<p><strong>Streams encontrados:</strong> ${data.streams_found}</p>`;
                        
                        data.streams.forEach((stream, index) => {
                            html += `
                                <div class="stream-item">
                                    <h4>🎬 Stream ${index + 1}</h4>
                                    <p><strong>Stream ID:</strong> ${stream.stream_id}</p>
                                    <p><strong>Canal:</strong> ${stream.channel || 'N/A'}</p>
                                    <p><strong>Outlet:</strong> ${stream.outlet || 'N/A'}</p>
                                    <p><strong>Idioma:</strong> ${stream.language || 'N/A'}</p>
                                    <p><strong>🎯 URL M3U8 Final:</strong></p>
                                    <div class="final-url">${stream.final_m3u8}</div>
                                </div>
                            `;
                        });
                    } else if (data.final_m3u8) {
                        // Resultado de conversión MPD
                        html += `
                            <p><strong>Stream ID:</strong> ${data.stream_id}</p>
                            <p><strong>Canal:</strong> ${data.channel || 'N/A'}</p>
                            <p><strong>Outlet:</strong> ${data.outlet || 'N/A'}</p>
                            <p><strong>Idioma:</strong> ${data.language || 'N/A'}</p>
                            <p><strong>🎯 URL M3U8 Final:</strong></p>
                            <div class="final-url">${data.final_m3u8}</div>
                        `;
                    } else if (data.message) {
                        // Mensaje informativo
                        html += `<p>${data.message}</p>`;
                        if (data.final_m3u8) {
                            html += `<div class="final-url">${data.final_m3u8}</div>`;
                        }
                    }
                    
                    html += '<p style="margin-top: 20px;"><strong>✅ Copia la URL M3U8 y úsala en tu reproductor de video</strong></p>';
                    
                    resultDiv.innerHTML = html;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<h3>❌ Error</h3><p>${data.error}</p>`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.style.display = 'block';
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `<h3>❌ Error de Conexión</h3><p>${error.message}</p>`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            });
        }
        
        function loadMpdExample() {
            document.getElementById('url-input').value = 'https://dch-ac-live.cdn.indazn.com/1ks3vz4xf76pe14jyqg85ou3tv/tv25f/stream.mpd?channel=1637&outlet=dazn-row&plang=es&dazn-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InV1aWRfMSJ9.eyJwYXRocyI6WyIvMWtzM3Z6NHhmNzZwZTE0anlxZzg1b3UzdHYiXSwiZXhjIjpbXSwiaGVhZGVycyI6W10sImNvIjpmYWxzZSwiaXAiOmZhbHNlLCJhc24iOmZhbHNlLCJpbnRzaWciOiJ2d2N0U2tZZzk2RXY1enoyZEJwVVBOUk5HdHVpa2V4VWRVcWRzOFBSeDU4IiwiaWF0IjoxNzUwMTc1NzQ4LCJleHAiOjE3NTAyNjIxNDh9.P3Wk4sbLJnnBeflB-0V6LzWzyllr2K09ccInIbGepCI';
            updateUrlType();
        }
        
        function loadEventExample() {
            document.getElementById('url-input').value = 'https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg';
            updateUrlType();
        }
        
        function clearAll() {
            document.getElementById('url-input').value = '';
            document.getElementById('result').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
            document.getElementById('url-type').innerHTML = '';
        }
    </script>
</body>
</html>
