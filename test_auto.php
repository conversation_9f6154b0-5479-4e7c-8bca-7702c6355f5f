<?php
require_once 'auto_extractor.php';

echo "=== PRUEBA DEL AUTO EXTRACTOR DAZN ===\n\n";

$eventUrl = 'https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg';

echo "URL del Evento:\n";
echo $eventUrl . "\n\n";

echo "🔍 Iniciando extracción automática...\n\n";

$extractor = new DaznAutoExtractor();
$result = $extractor->extractFromEventUrl($eventUrl);

if ($result['success']) {
    echo "✅ EXTRACCIÓN EXITOSA!\n\n";
    echo "Streams encontrados: " . $result['streams_found'] . "\n\n";
    
    foreach ($result['streams'] as $i => $stream) {
        echo "=== STREAM " . ($i + 1) . " ===\n";
        echo "Stream ID: " . $stream['stream_id'] . "\n";
        echo "Canal: " . ($stream['channel'] ?? 'N/A') . "\n";
        echo "Outlet: " . ($stream['outlet'] ?? 'N/A') . "\n";
        echo "Idioma: " . ($stream['language'] ?? 'N/A') . "\n";
        echo "URL M3U8: " . $stream['final_m3u8'] . "\n\n";
    }
    
    echo "🎯 URL PRINCIPAL PARA USAR:\n";
    echo $result['primary_stream']['final_m3u8'] . "\n\n";
    
} else {
    echo "❌ ERROR: " . $result['error'] . "\n";
}

echo "=== FIN DE LA PRUEBA ===\n";
?>
