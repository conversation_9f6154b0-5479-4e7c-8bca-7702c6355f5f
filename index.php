<?php
/**
 * Interfaz web para DAZN Stream Extractor
 * Optimizado para Hostinger
 */

require_once 'dazn_stream_extractor.php';

// Configuración
$config = [
    'email' => '<EMAIL>',
    'password' => 'RVTbr9kXdZCaXnq',
    'debug' => true
];

// Función para mostrar resultados en JSON
function jsonResponse($data, $success = true) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Manejo de peticiones AJAX
if (isset($_GET['action'])) {
    $extractor = new DaznStreamExtractor($config['email'], $config['password']);
    
    switch ($_GET['action']) {
        case 'convert':
            $mpdUrl = $_GET['mpd_url'] ?? '';
            if (empty($mpdUrl)) {
                jsonResponse('URL MPD requerida', false);
            }
            
            $streamId = $extractor->extractStreamId($mpdUrl);
            $m3u8Url = $extractor->convertMpdToM3u8($mpdUrl);
            
            jsonResponse([
                'original_mpd' => $mpdUrl,
                'stream_id' => $streamId,
                'converted_m3u8' => $m3u8Url
            ]);
            break;
            
        case 'login':
            $success = $extractor->login();
            jsonResponse(['logged_in' => $success]);
            break;
            
        case 'events':
            if ($extractor->login()) {
                $events = $extractor->getEvents();
                jsonResponse($events);
            } else {
                jsonResponse('Error en login', false);
            }
            break;
            
        case 'stream':
            $eventId = $_GET['event_id'] ?? '';
            if (empty($eventId)) {
                jsonResponse('Event ID requerido', false);
            }
            
            if ($extractor->login()) {
                $streamData = $extractor->getStreamUrl($eventId);
                jsonResponse($streamData);
            } else {
                jsonResponse('Error en login', false);
            }
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAZN Stream Extractor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            display: none;
            color: #007bff;
        }
    </style>
</head>
<body>
    <h1>🎬 DAZN Stream Extractor</h1>
    <p><strong>Nota:</strong> Script educativo para demostración técnica. Usar responsablemente.</p>
    
    <!-- Convertidor MPD a M3U8 -->
    <div class="container">
        <h2>🔄 Convertir MPD a M3U8</h2>
        <div class="form-group">
            <label for="mpd-url">URL MPD:</label>
            <textarea id="mpd-url" rows="3" placeholder="https://dcd-ak-livedazn.akamaized.net/..."></textarea>
        </div>
        <button onclick="convertUrl()">Convertir</button>
        <div class="loading" id="convert-loading">Procesando...</div>
        <div id="convert-result" class="result" style="display:none;"></div>
    </div>
    
    <!-- Login y Eventos -->
    <div class="container">
        <h2>🔐 Login y Eventos</h2>
        <button onclick="testLogin()">Probar Login</button>
        <button onclick="getEvents()">Obtener Eventos</button>
        <div class="loading" id="events-loading">Cargando...</div>
        <div id="events-result" class="result" style="display:none;"></div>
    </div>
    
    <!-- Obtener Stream -->
    <div class="container">
        <h2>📺 Obtener Stream</h2>
        <div class="form-group">
            <label for="event-id">Event ID:</label>
            <input type="text" id="event-id" placeholder="Ingresa el ID del evento">
        </div>
        <button onclick="getStream()">Obtener Stream</button>
        <div class="loading" id="stream-loading">Obteniendo stream...</div>
        <div id="stream-result" class="result" style="display:none;"></div>
    </div>

    <script>
        function showLoading(id) {
            document.getElementById(id).style.display = 'block';
        }
        
        function hideLoading(id) {
            document.getElementById(id).style.display = 'none';
        }
        
        function showResult(id, content, isSuccess = true) {
            const element = document.getElementById(id);
            element.textContent = content;
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
            element.style.display = 'block';
        }
        
        async function convertUrl() {
            const mpdUrl = document.getElementById('mpd-url').value.trim();
            if (!mpdUrl) {
                alert('Por favor ingresa una URL MPD');
                return;
            }
            
            showLoading('convert-loading');
            
            try {
                const response = await fetch(`?action=convert&mpd_url=${encodeURIComponent(mpdUrl)}`);
                const data = await response.json();
                
                if (data.success) {
                    const result = `Stream ID: ${data.data.stream_id}
                    
URL M3U8: ${data.data.converted_m3u8}

URL MPD Original: ${data.data.original_mpd}`;
                    showResult('convert-result', result, true);
                } else {
                    showResult('convert-result', 'Error: ' + data.data, false);
                }
            } catch (error) {
                showResult('convert-result', 'Error de conexión: ' + error.message, false);
            }
            
            hideLoading('convert-loading');
        }
        
        async function testLogin() {
            showLoading('events-loading');
            
            try {
                const response = await fetch('?action=login');
                const data = await response.json();
                
                const result = data.success ? 
                    `Login ${data.data.logged_in ? 'exitoso' : 'fallido'}` :
                    'Error: ' + data.data;
                    
                showResult('events-result', result, data.success && data.data.logged_in);
            } catch (error) {
                showResult('events-result', 'Error: ' + error.message, false);
            }
            
            hideLoading('events-loading');
        }
        
        async function getEvents() {
            showLoading('events-loading');
            
            try {
                const response = await fetch('?action=events');
                const data = await response.json();
                
                if (data.success) {
                    const events = data.data.slice(0, 10); // Mostrar solo los primeros 10
                    const result = events.map(event => 
                        `${event.title} (ID: ${event.id}) - ${event.isLive ? 'EN VIVO' : 'NO VIVO'}`
                    ).join('\n');
                    showResult('events-result', result || 'No se encontraron eventos', true);
                } else {
                    showResult('events-result', 'Error: ' + data.data, false);
                }
            } catch (error) {
                showResult('events-result', 'Error: ' + error.message, false);
            }
            
            hideLoading('events-loading');
        }
        
        async function getStream() {
            const eventId = document.getElementById('event-id').value.trim();
            if (!eventId) {
                alert('Por favor ingresa un Event ID');
                return;
            }
            
            showLoading('stream-loading');
            
            try {
                const response = await fetch(`?action=stream&event_id=${encodeURIComponent(eventId)}`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    const result = `Stream ID: ${data.data.streamId}

URL M3U8: ${data.data.m3u8}

URL MPD: ${data.data.mpd}`;
                    showResult('stream-result', result, true);
                } else {
                    showResult('stream-result', 'Error: ' + (data.data || 'No se pudo obtener el stream'), false);
                }
            } catch (error) {
                showResult('stream-result', 'Error: ' + error.message, false);
            }
            
            hideLoading('stream-loading');
        }
    </script>
</body>
</html>
