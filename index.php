<?php
/**
 * DAZN Ultimate Converter - Página Principal
 * Extrae automáticamente desde URLs de eventos y convierte MPD a M3U8
 */

require_once 'converter.php';
require_once 'simple_auto.php';

// Función para mostrar resultados en JSON
function jsonResponse($data, $success = true) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Detectar tipo de URL
function detectUrlType($url) {
    if (strpos($url, '.mpd') !== false) {
        return 'mpd';
    } elseif (strpos($url, 'dazn.com') !== false && strpos($url, 'fixture') !== false) {
        return 'event';
    } elseif (strpos($url, '.m3u8') !== false) {
        return 'm3u8';
    }
    return 'unknown';
}

// API para extracción automática desde URLs de eventos
if (isset($_POST['event_url']) || isset($_GET['event_url'])) {
    $eventUrl = $_POST['event_url'] ?? $_GET['event_url'] ?? '';

    if (empty($eventUrl)) {
        jsonResponse('URL del evento requerida', false);
    }

    $extractor = new SimpleDaznExtractor();
    $result = $extractor->extractFromEventUrl($eventUrl);

    if ($result['success']) {
        jsonResponse($result);
    } else {
        jsonResponse($result['error'], false);
    }
}

// API para conversión MPD (herramientas avanzadas)
if (isset($_POST['mpd_url']) || isset($_GET['mpd_url'])) {
    $mpdUrl = $_POST['mpd_url'] ?? $_GET['mpd_url'] ?? '';

    if (empty($mpdUrl)) {
        jsonResponse('URL MPD requerida', false);
    }

    $result = SimpleConverter::process($mpdUrl);

    if ($result['success']) {
        jsonResponse($result);
    } else {
        jsonResponse($result['error'], false);
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAZN Ultimate Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .main-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 3px solid #2196F3;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .advanced-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="url"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input[type="url"]:focus, textarea:focus {
            border-color: #2196F3;
            outline: none;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result {
            background: white;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }
        .success {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        }
        .error {
            border-color: #f44336;
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
        }
        .stream-item {
            border: 2px solid #2196F3;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }
        .final-url {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 3px solid #4CAF50;
            padding: 15px;
            border-radius: 10px;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            font-weight: bold;
            margin: 10px 0;
        }
        .loading {
            display: none;
            text-align: center;
            color: #2196F3;
            font-weight: bold;
            font-size: 16px;
            padding: 20px;
        }
        .url-type {
            display: inline-block;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .type-event { background: #4CAF50; color: white; }
        .type-mpd { background: #FF9800; color: white; }
        .type-m3u8 { background: #2196F3; color: white; }
        .priority-badge {
            background: #4CAF50;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .advanced-badge {
            background: #FF9800;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 DAZN Stream Extractor</h1>
        <p>Extrae automáticamente streams desde URLs de eventos de DAZN</p>
    </div>

    <!-- Sección Principal: Simple DAZN Auto Extractor -->
    <div class="main-section">
        <h2>🚀 Simple DAZN Auto Extractor <span class="priority-badge">PRINCIPAL</span></h2>
        <p><strong>Pega la URL completa del evento de DAZN y obtén el stream automáticamente (sin necesidad de F12)</strong></p>

        <div class="form-group">
            <label for="event-url">🔗 URL del Evento de DAZN:</label>
            <input type="url" id="event-url" placeholder="https://www.dazn.com/es-PA/fixture/..."
                   value="https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg">
        </div>

        <div class="button-group">
            <button onclick="extractFromEvent()">🎯 Extraer Stream Automáticamente</button>
            <button onclick="loadEventExample()">📝 Cargar Ejemplo</button>
            <button onclick="clearEvent()">🗑️ Limpiar</button>
        </div>

        <div class="loading" id="event-loading">
            <div>🔄 Extrayendo stream automáticamente...</div>
            <div style="font-size: 14px; margin-top: 10px;">Analizando página del evento...</div>
        </div>

        <div id="event-result"></div>
    </div>

    <!-- Instrucciones -->
    <div class="container">
        <h3>📋 Cómo usar el Simple DAZN Auto Extractor:</h3>
        <ol>
            <li><strong>Ve al evento en DAZN</strong> - Abre el evento que quieres ver en tu navegador</li>
            <li><strong>Copia la URL completa</strong> - Desde la barra de direcciones del navegador</li>
            <li><strong>Pégala arriba</strong> y haz clic en "Extraer Stream Automáticamente"</li>
            <li><strong>¡Listo!</strong> Obtienes la URL M3U8 sin necesidad de F12</li>
            <li><strong>Usa la URL M3U8</strong> en tu reproductor de video favorito</li>
        </ol>

        <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h4 style="color: #0066cc; margin-top: 0;">💡 ¿Por qué usar el Auto Extractor?</h4>
            <ul style="margin-bottom: 0;">
                <li>✅ <strong>Sin F12</strong> - No necesitas herramientas de desarrollador</li>
                <li>✅ <strong>Automático</strong> - El script hace todo el trabajo por ti</li>
                <li>✅ <strong>Rápido</strong> - Proceso en segundos</li>
                <li>✅ <strong>Funciona siempre</strong> - Compatible con cualquier evento de DAZN</li>
            </ul>
        </div>
    </div>

    <!-- Sección Avanzada: Para usuarios que ya tienen URLs MPD -->
    <div class="container">
        <div class="advanced-section">
            <h2>🔧 Herramientas Avanzadas <span class="advanced-badge">USUARIOS AVANZADOS</span></h2>
            <p><em>Para usuarios que ya tienen URLs MPD desde F12</em></p>

            <div class="form-group">
                <label for="mpd-url">📺 URL MPD (desde F12):</label>
                <textarea id="mpd-url" rows="3" placeholder="Pega aquí tu URL MPD si ya la tienes..."></textarea>
            </div>

            <div class="button-group">
                <button onclick="convertMpd()">🔄 Convertir MPD a M3U8</button>
                <button onclick="loadMpdExample()">📝 Ejemplo MPD</button>
            </div>

            <div class="loading" id="mpd-loading">Convirtiendo MPD...</div>
            <div id="mpd-result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // Función principal: Extraer desde URL de evento
        async function extractFromEvent() {
            const eventUrl = document.getElementById('event-url').value.trim();
            if (!eventUrl) {
                alert('Por favor ingresa la URL del evento');
                return;
            }

            document.getElementById('event-loading').style.display = 'block';
            document.getElementById('event-result').style.display = 'none';

            try {
                const response = await fetch('index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'event_url=' + encodeURIComponent(eventUrl)
                });

                const data = await response.json();

                document.getElementById('event-loading').style.display = 'none';
                const resultDiv = document.getElementById('event-result');

                if (data.success) {
                    let html = '<h3>✅ Extracción Automática Exitosa</h3>';
                    html += `<p><strong>Streams encontrados:</strong> ${data.data.streams_found}</p>`;

                    data.data.streams.forEach((stream, index) => {
                        html += `
                            <div class="stream-item">
                                <h4>🎬 Stream ${index + 1}</h4>
                                <p><strong>Stream ID:</strong> ${stream.stream_id}</p>
                                <p><strong>Canal:</strong> ${stream.channel || 'N/A'}</p>
                                <p><strong>Outlet:</strong> ${stream.outlet || 'N/A'}</p>
                                <p><strong>Idioma:</strong> ${stream.language || 'N/A'}</p>
                                <p><strong>🎯 URL M3U8 Final:</strong></p>
                                <div class="final-url">${stream.final_m3u8}</div>
                            </div>
                        `;
                    });

                    html += '<p style="margin-top: 20px; font-weight: bold; color: #4CAF50;">✅ Copia la URL M3U8 y úsala en tu reproductor de video</p>';

                    resultDiv.innerHTML = html;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<h3>❌ Error</h3><p>${data.data}</p>`;
                    resultDiv.className = 'result error';
                }

                resultDiv.style.display = 'block';
            } catch (error) {
                document.getElementById('event-loading').style.display = 'none';
                const resultDiv = document.getElementById('event-result');
                resultDiv.innerHTML = `<h3>❌ Error de Conexión</h3><p>${error.message}</p>`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        // Función para convertir MPD (sección avanzada)
        async function convertMpd() {
            const mpdUrl = document.getElementById('mpd-url').value.trim();
            if (!mpdUrl) {
                alert('Por favor ingresa una URL MPD');
                return;
            }

            document.getElementById('mpd-loading').style.display = 'block';
            document.getElementById('mpd-result').style.display = 'none';

            try {
                const response = await fetch('index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'mpd_url=' + encodeURIComponent(mpdUrl)
                });

                const data = await response.json();

                document.getElementById('mpd-loading').style.display = 'none';
                const resultDiv = document.getElementById('mpd-result');

                if (data.success) {
                    const result = `
                        <h4>✅ Conversión MPD Exitosa</h4>
                        <p><strong>Stream ID:</strong> ${data.data.stream_id}</p>
                        <p><strong>Canal:</strong> ${data.data.channel || 'N/A'}</p>
                        <p><strong>Outlet:</strong> ${data.data.outlet || 'N/A'}</p>
                        <p><strong>Idioma:</strong> ${data.data.language || 'N/A'}</p>
                        <p><strong>🎯 URL M3U8 Final:</strong></p>
                        <div class="final-url">${data.data.final_m3u8}</div>
                        <p style="margin-top: 15px; font-weight: bold; color: #4CAF50;">✅ Copia la URL M3U8 y úsala en tu reproductor</p>
                    `;
                    resultDiv.innerHTML = result;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<h4>❌ Error</h4><p>${data.data}</p>`;
                    resultDiv.className = 'result error';
                }

                resultDiv.style.display = 'block';
            } catch (error) {
                document.getElementById('mpd-loading').style.display = 'none';
                const resultDiv = document.getElementById('mpd-result');
                resultDiv.innerHTML = `<h4>❌ Error de Conexión</h4><p>${error.message}</p>`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        // Funciones de utilidad
        function loadEventExample() {
            document.getElementById('event-url').value = 'https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg';
        }

        function loadMpdExample() {
            document.getElementById('mpd-url').value = 'https://dch-ac-live.cdn.indazn.com/1ks3vz4xf76pe14jyqg85ou3tv/tv25f/stream.mpd?channel=1637&outlet=dazn-row&plang=es&dazn-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InV1aWRfMSJ9.eyJwYXRocyI6WyIvMWtzM3Z6NHhmNzZwZTE0anlxZzg1b3UzdHYiXSwiZXhjIjpbXSwiaGVhZGVycyI6W10sImNvIjpmYWxzZSwiaXAiOmZhbHNlLCJhc24iOmZhbHNlLCJpbnRzaWciOiJ2d2N0U2tZZzk2RXY1enoyZEJwVVBOUk5HdHVpa2V4VWRVcWRzOFBSeDU4IiwiaWF0IjoxNzUwMTc1NzQ4LCJleHAiOjE3NTAyNjIxNDh9.P3Wk4sbLJnnBeflB-0V6LzWzyllr2K09ccInIbGepCI';
        }

        function clearEvent() {
            document.getElementById('event-url').value = '';
            document.getElementById('event-result').style.display = 'none';
            document.getElementById('event-loading').style.display = 'none';
        }
    </script>
</body>
</html>
