<?php
/**
 * Interfaz web para DAZN Stream Extractor
 * Optimizado para Hostinger
 */

require_once 'dazn_stream_extractor.php';
require_once 'simple_auto.php';

// Configuración
$config = [
    'email' => '<EMAIL>',
    'password' => 'RVTbr9kXdZCaXnq',
    'debug' => true
];

// Función para mostrar resultados en JSON
function jsonResponse($data, $success = true) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// API para extracción automática desde URLs de eventos (NUEVA FUNCIONALIDAD)
if (isset($_POST['event_url']) || isset($_GET['event_url'])) {
    $eventUrl = $_POST['event_url'] ?? $_GET['event_url'] ?? '';

    if (empty($eventUrl)) {
        jsonResponse('URL del evento requerida', false);
    }

    $extractor = new SimpleDaznExtractor();
    $result = $extractor->extractFromEventUrl($eventUrl);

    if ($result['success']) {
        jsonResponse($result);
    } else {
        jsonResponse($result['error'], false);
    }
}

// Manejo de peticiones AJAX (FUNCIONALIDAD ORIGINAL)
if (isset($_GET['action'])) {
    $extractor = new DaznStreamExtractor($config['email'], $config['password']);

    switch ($_GET['action']) {
        case 'convert':
            $mpdUrl = $_GET['mpd_url'] ?? '';
            if (empty($mpdUrl)) {
                jsonResponse('URL MPD requerida', false);
            }

            $streamId = $extractor->extractStreamId($mpdUrl);
            $m3u8Url = $extractor->convertMpdToM3u8($mpdUrl);

            jsonResponse([
                'original_mpd' => $mpdUrl,
                'stream_id' => $streamId,
                'converted_m3u8' => $m3u8Url
            ]);
            break;

        case 'login':
            $success = $extractor->login();
            jsonResponse(['logged_in' => $success]);
            break;

        case 'events':
            if ($extractor->login()) {
                $events = $extractor->getEvents();
                jsonResponse($events);
            } else {
                jsonResponse('Error en login', false);
            }
            break;

        case 'stream':
            $eventId = $_GET['event_id'] ?? '';
            if (empty($eventId)) {
                jsonResponse('Event ID requerido', false);
            }

            if ($extractor->login()) {
                $streamData = $extractor->getStreamUrl($eventId);
                jsonResponse($streamData);
            } else {
                jsonResponse('Error en login', false);
            }
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAZN Stream Extractor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .priority-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 3px solid #2196F3;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="url"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            display: none;
            color: #007bff;
        }
        .priority-badge {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .stream-item {
            border: 2px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .final-url {
            background: #d4edda;
            border: 2px solid #28a745;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎬 DAZN Stream Extractor</h1>
    <p><strong>Nota:</strong> Script educativo para demostración técnica. Usar responsablemente.</p>

    <!-- Sección Principal: Simple DAZN Auto Extractor -->
    <div class="priority-section">
        <h2>🚀 Simple DAZN Auto Extractor <span class="priority-badge">RECOMENDADO</span></h2>
        <p><strong>Pega la URL completa del evento de DAZN y obtén el stream automáticamente (sin necesidad de F12)</strong></p>

        <div class="form-group">
            <label for="event-url">🔗 URL del Evento de DAZN:</label>
            <input type="url" id="event-url" placeholder="https://www.dazn.com/es-PA/fixture/..."
                   value="https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg">
        </div>

        <button onclick="extractFromEvent()">🎯 Extraer Stream Automáticamente</button>
        <button onclick="loadEventExample()">📝 Cargar Ejemplo</button>
        <button onclick="clearEvent()">🗑️ Limpiar</button>

        <div class="loading" id="event-loading">🔄 Extrayendo stream automáticamente...</div>
        <div id="event-result" class="result" style="display:none;"></div>
    </div>

    <!-- Convertidor MPD a M3U8 -->
    <div class="container">
        <h2>🔄 Convertir MPD a M3U8</h2>
        <div class="form-group">
            <label for="mpd-url">URL MPD:</label>
            <textarea id="mpd-url" rows="3" placeholder="https://dcd-ak-livedazn.akamaized.net/..."></textarea>
        </div>
        <button onclick="convertUrl()">Convertir</button>
        <div class="loading" id="convert-loading">Procesando...</div>
        <div id="convert-result" class="result" style="display:none;"></div>
    </div>

    <!-- Login y Eventos -->
    <div class="container">
        <h2>🔐 Login y Eventos</h2>
        <button onclick="testLogin()">Probar Login</button>
        <button onclick="getEvents()">Obtener Eventos</button>
        <div class="loading" id="events-loading">Cargando...</div>
        <div id="events-result" class="result" style="display:none;"></div>
    </div>

    <!-- Obtener Stream -->
    <div class="container">
        <h2>📺 Obtener Stream</h2>
        <div class="form-group">
            <label for="event-id">Event ID:</label>
            <input type="text" id="event-id" placeholder="Ingresa el ID del evento">
        </div>
        <button onclick="getStream()">Obtener Stream</button>
        <div class="loading" id="stream-loading">Obteniendo stream...</div>
        <div id="stream-result" class="result" style="display:none;"></div>
    </div>

    <script>
        // Funciones de utilidad
        function showLoading(id) {
            document.getElementById(id).style.display = 'block';
        }

        function hideLoading(id) {
            document.getElementById(id).style.display = 'none';
        }

        function showResult(id, content, isSuccess = true) {
            const element = document.getElementById(id);
            element.textContent = content;
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
            element.style.display = 'block';
        }

        // NUEVA FUNCIONALIDAD: Extraer desde URL de evento
        async function extractFromEvent() {
            const eventUrl = document.getElementById('event-url').value.trim();
            if (!eventUrl) {
                alert('Por favor ingresa la URL del evento');
                return;
            }

            showLoading('event-loading');
            document.getElementById('event-result').style.display = 'none';

            try {
                const response = await fetch('index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'event_url=' + encodeURIComponent(eventUrl)
                });

                const data = await response.json();

                hideLoading('event-loading');
                const resultDiv = document.getElementById('event-result');

                if (data.success) {
                    let result = `✅ EXTRACCIÓN AUTOMÁTICA EXITOSA!

Streams encontrados: ${data.data.streams_found}

`;

                    data.data.streams.forEach((stream, index) => {
                        result += `=== STREAM ${index + 1} ===
Stream ID: ${stream.stream_id}
Canal: ${stream.channel || 'N/A'}
Outlet: ${stream.outlet || 'N/A'}
Idioma: ${stream.language || 'N/A'}

🎯 URL M3U8 FINAL:
${stream.final_m3u8}

`;
                    });

                    result += '✅ Copia la URL M3U8 y úsala en tu reproductor de video';

                    showResult('event-result', result, true);
                } else {
                    showResult('event-result', 'Error: ' + data.data, false);
                }
            } catch (error) {
                hideLoading('event-loading');
                showResult('event-result', 'Error de conexión: ' + error.message, false);
            }
        }

        function loadEventExample() {
            document.getElementById('event-url').value = 'https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg';
        }

        function clearEvent() {
            document.getElementById('event-url').value = '';
            document.getElementById('event-result').style.display = 'none';
            hideLoading('event-loading');
        }

        // FUNCIONALIDAD ORIGINAL: Convertir MPD a M3U8
        async function convertUrl() {
            const mpdUrl = document.getElementById('mpd-url').value.trim();
            if (!mpdUrl) {
                alert('Por favor ingresa una URL MPD');
                return;
            }

            showLoading('convert-loading');

            try {
                const response = await fetch(`?action=convert&mpd_url=${encodeURIComponent(mpdUrl)}`);
                const data = await response.json();

                if (data.success) {
                    const result = `Stream ID: ${data.data.stream_id}

URL M3U8: ${data.data.converted_m3u8}

URL MPD Original: ${data.data.original_mpd}`;
                    showResult('convert-result', result, true);
                } else {
                    showResult('convert-result', 'Error: ' + data.data, false);
                }
            } catch (error) {
                showResult('convert-result', 'Error de conexión: ' + error.message, false);
            }

            hideLoading('convert-loading');
        }

        async function testLogin() {
            showLoading('events-loading');

            try {
                const response = await fetch('?action=login');
                const data = await response.json();

                const result = data.success ?
                    `Login ${data.data.logged_in ? 'exitoso' : 'fallido'}` :
                    'Error: ' + data.data;

                showResult('events-result', result, data.success && data.data.logged_in);
            } catch (error) {
                showResult('events-result', 'Error: ' + error.message, false);
            }

            hideLoading('events-loading');
        }

        async function getEvents() {
            showLoading('events-loading');

            try {
                const response = await fetch('?action=events');
                const data = await response.json();

                if (data.success) {
                    const events = data.data.slice(0, 10); // Mostrar solo los primeros 10
                    const result = events.map(event =>
                        `${event.title} (ID: ${event.id}) - ${event.isLive ? 'EN VIVO' : 'NO VIVO'}`
                    ).join('\n');
                    showResult('events-result', result || 'No se encontraron eventos', true);
                } else {
                    showResult('events-result', 'Error: ' + data.data, false);
                }
            } catch (error) {
                showResult('events-result', 'Error: ' + error.message, false);
            }

            hideLoading('events-loading');
        }

        async function getStream() {
            const eventId = document.getElementById('event-id').value.trim();
            if (!eventId) {
                alert('Por favor ingresa un Event ID');
                return;
            }

            showLoading('stream-loading');

            try {
                const response = await fetch(`?action=stream&event_id=${encodeURIComponent(eventId)}`);
                const data = await response.json();

                if (data.success && data.data) {
                    const result = `Stream ID: ${data.data.streamId}

URL M3U8: ${data.data.m3u8}

URL MPD: ${data.data.mpd}`;
                    showResult('stream-result', result, true);
                } else {
                    showResult('stream-result', 'Error: ' + (data.data || 'No se pudo obtener el stream'), false);
                }
            } catch (error) {
                showResult('stream-result', 'Error: ' + error.message, false);
            }

            hideLoading('stream-loading');
        }
    </script>
</body>
</html>
