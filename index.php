<?php
/**
 * DAZN Ultimate Converter - Página Principal
 * Extrae automáticamente desde URLs de eventos y convierte MPD a M3U8
 */

require_once 'converter.php';
require_once 'simple_auto.php';

// Función para mostrar resultados en JSON
function jsonResponse($data, $success = true) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Detectar tipo de URL
function detectUrlType($url) {
    if (strpos($url, '.mpd') !== false) {
        return 'mpd';
    } elseif (strpos($url, 'dazn.com') !== false && strpos($url, 'fixture') !== false) {
        return 'event';
    } elseif (strpos($url, '.m3u8') !== false) {
        return 'm3u8';
    }
    return 'unknown';
}

// API unificada para manejar cualquier tipo de URL
if (isset($_POST['url']) || isset($_GET['url'])) {
    $url = $_POST['url'] ?? $_GET['url'] ?? '';

    if (empty($url)) {
        jsonResponse('URL requerida', false);
    }

    $urlType = detectUrlType($url);

    switch ($urlType) {
        case 'mpd':
            // Convertir MPD directamente
            $result = SimpleConverter::process($url);
            if ($result['success']) {
                jsonResponse($result);
            } else {
                jsonResponse($result['error'], false);
            }
            break;

        case 'event':
            // Extraer desde URL de evento
            $extractor = new SimpleDaznExtractor();
            $result = $extractor->extractFromEventUrl($url);
            if ($result['success']) {
                jsonResponse($result);
            } else {
                jsonResponse($result['error'], false);
            }
            break;

        case 'm3u8':
            // Ya es M3U8, solo extraer información
            jsonResponse([
                'message' => 'La URL ya está en formato M3U8',
                'final_m3u8' => $url,
                'stream_id' => 'N/A'
            ]);
            break;

        default:
            jsonResponse('Tipo de URL no reconocido. Usa URLs de eventos DAZN o URLs MPD.', false);
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAZN Ultimate Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .main-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 3px solid #2196F3;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .advanced-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="url"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input[type="url"]:focus, textarea:focus {
            border-color: #2196F3;
            outline: none;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result {
            background: white;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }
        .success {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        }
        .error {
            border-color: #f44336;
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
        }
        .stream-item {
            border: 2px solid #2196F3;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }
        .final-url {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 3px solid #4CAF50;
            padding: 15px;
            border-radius: 10px;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            font-weight: bold;
            margin: 10px 0;
        }
        .loading {
            display: none;
            text-align: center;
            color: #2196F3;
            font-weight: bold;
            font-size: 16px;
            padding: 20px;
        }
        .url-type {
            display: inline-block;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .type-event { background: #4CAF50; color: white; }
        .type-mpd { background: #FF9800; color: white; }
        .type-m3u8 { background: #2196F3; color: white; }
        .priority-badge {
            background: #4CAF50;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .advanced-badge {
            background: #FF9800;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 DAZN Ultimate Converter</h1>
        <p>Extrae automáticamente streams desde URLs de eventos de DAZN</p>
    </div>

    <!-- Sección Principal: Extracción Automática -->
    <div class="main-section">
        <h2>🚀 Extracción Automática <span class="priority-badge">RECOMENDADO</span></h2>
        <p><strong>Pega la URL completa del evento de DAZN y obtén el stream automáticamente</strong></p>

        <div class="form-group">
            <label for="event-url">🔗 URL del Evento de DAZN:</label>
            <input type="url" id="event-url" placeholder="https://www.dazn.com/es-PA/fixture/..."
                   value="https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg">
            <div id="url-type" style="margin-top: 10px;"></div>
        </div>

        <div class="button-group">
            <button onclick="processMainUrl()">🎯 Extraer Stream Automáticamente</button>
            <button onclick="loadEventExample()">📝 Cargar Ejemplo</button>
            <button onclick="clearMain()">🗑️ Limpiar</button>
        </div>

        <div class="loading" id="main-loading">
            <div>🔄 Extrayendo stream automáticamente...</div>
            <div style="font-size: 14px; margin-top: 10px;">Analizando página del evento...</div>
        </div>

        <div id="main-result"></div>
    </div>

    <!-- Instrucciones -->
    <div class="container">
        <h3>📋 Cómo usar:</h3>
        <ol>
            <li><strong>Copia la URL completa del evento</strong> desde tu navegador cuando estés viendo el evento en DAZN</li>
            <li><strong>Pégala en el campo de arriba</strong> y haz clic en "Extraer Stream Automáticamente"</li>
            <li><strong>¡Listo!</strong> El sistema extraerá automáticamente la URL M3U8 sin necesidad de F12</li>
            <li><strong>Copia la URL M3U8</strong> resultante y úsala en tu reproductor de video</li>
        </ol>

        <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h4 style="color: #0066cc; margin-top: 0;">💡 Ventajas de la Extracción Automática:</h4>
            <ul style="margin-bottom: 0;">
                <li>✅ <strong>No necesitas F12</strong> - Solo pega la URL del evento</li>
                <li>✅ <strong>Proceso automático</strong> - El script hace todo el trabajo</li>
                <li>✅ <strong>Más rápido</strong> - No necesitas buscar manualmente</li>
                <li>✅ <strong>Siempre actualizado</strong> - Funciona con cualquier evento</li>
            </ul>
        </div>
    </div>

    <!-- Sección Avanzada: Para usuarios que ya tienen URLs MPD -->
    <div class="container">
        <div class="advanced-section">
            <h2>🔧 Herramientas Avanzadas <span class="advanced-badge">USUARIOS AVANZADOS</span></h2>
            <p><em>Para usuarios que ya tienen URLs MPD desde F12</em></p>

            <div class="form-group">
                <label for="mpd-url">📺 URL MPD (desde F12):</label>
                <textarea id="mpd-url" rows="3" placeholder="Pega aquí tu URL MPD si ya la tienes..."></textarea>
            </div>

            <div class="button-group">
                <button onclick="convertMpd()">🔄 Convertir MPD a M3U8</button>
                <button onclick="loadMpdExample()">📝 Ejemplo MPD</button>
            </div>

            <div class="loading" id="mpd-loading">Convirtiendo MPD...</div>
            <div id="mpd-result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // Detectar tipo de URL
        function detectUrlType(url) {
            if (url.includes('.mpd')) return 'mpd';
            if (url.includes('dazn.com') && url.includes('fixture')) return 'event';
            if (url.includes('.m3u8')) return 'm3u8';
            return 'unknown';
        }

        // Actualizar indicador de tipo de URL
        function updateUrlType() {
            const url = document.getElementById('event-url').value.trim();
            const typeDiv = document.getElementById('url-type');

            if (!url) {
                typeDiv.innerHTML = '';
                return;
            }

            const type = detectUrlType(url);
            let typeHtml = '';

            switch (type) {
                case 'event':
                    typeHtml = '<span class="url-type type-event">🎯 URL de Evento - Se extraerá automáticamente</span>';
                    break;
                case 'mpd':
                    typeHtml = '<span class="url-type type-mpd">📺 URL MPD - Se convertirá a M3U8</span>';
                    break;
                case 'm3u8':
                    typeHtml = '<span class="url-type type-m3u8">🎵 URL M3U8 - Se validará</span>';
                    break;
                default:
                    typeHtml = '<span class="url-type" style="background: #f44336; color: white;">❓ Tipo de URL no reconocido</span>';
                    break;
            }

            typeDiv.innerHTML = typeHtml;
        }

        // Detectar tipo mientras se escribe
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('event-url').addEventListener('input', updateUrlType);
            updateUrlType(); // Ejecutar al cargar
        });

        // Función principal para procesar URL
        async function processMainUrl() {
            const url = document.getElementById('event-url').value.trim();
            if (!url) {
                alert('Por favor ingresa una URL');
                return;
            }

            document.getElementById('main-loading').style.display = 'block';
            document.getElementById('main-result').style.display = 'none';

            try {
                const response = await fetch('index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'url=' + encodeURIComponent(url)
                });

                const data = await response.json();

                document.getElementById('main-loading').style.display = 'none';
                const resultDiv = document.getElementById('main-result');

                if (data.success) {
                    let html = '<h3>✅ Extracción Exitosa</h3>';

                    if (data.data.streams) {
                        // Resultado de extracción de evento
                        html += `<p><strong>Streams encontrados:</strong> ${data.data.streams_found}</p>`;

                        data.data.streams.forEach((stream, index) => {
                            html += `
                                <div class="stream-item">
                                    <h4>🎬 Stream ${index + 1}</h4>
                                    <p><strong>Stream ID:</strong> ${stream.stream_id}</p>
                                    <p><strong>Canal:</strong> ${stream.channel || 'N/A'}</p>
                                    <p><strong>Outlet:</strong> ${stream.outlet || 'N/A'}</p>
                                    <p><strong>Idioma:</strong> ${stream.language || 'N/A'}</p>
                                    <p><strong>🎯 URL M3U8 Final:</strong></p>
                                    <div class="final-url">${stream.final_m3u8}</div>
                                </div>
                            `;
                        });
                    } else if (data.data.final_m3u8) {
                        // Resultado de conversión MPD
                        html += `
                            <div class="stream-item">
                                <h4>🎬 Stream Convertido</h4>
                                <p><strong>Stream ID:</strong> ${data.data.stream_id}</p>
                                <p><strong>Canal:</strong> ${data.data.channel || 'N/A'}</p>
                                <p><strong>Outlet:</strong> ${data.data.outlet || 'N/A'}</p>
                                <p><strong>Idioma:</strong> ${data.data.language || 'N/A'}</p>
                                <p><strong>🎯 URL M3U8 Final:</strong></p>
                                <div class="final-url">${data.data.final_m3u8}</div>
                            </div>
                        `;
                    } else if (data.data.message) {
                        html += `<p>${data.data.message}</p>`;
                        if (data.data.final_m3u8) {
                            html += `<div class="final-url">${data.data.final_m3u8}</div>`;
                        }
                    }

                    html += '<p style="margin-top: 20px; font-weight: bold; color: #4CAF50;">✅ Copia la URL M3U8 y úsala en tu reproductor de video</p>';

                    resultDiv.innerHTML = html;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<h3>❌ Error</h3><p>${data.data}</p>`;
                    resultDiv.className = 'result error';
                }

                resultDiv.style.display = 'block';
            } catch (error) {
                document.getElementById('main-loading').style.display = 'none';
                const resultDiv = document.getElementById('main-result');
                resultDiv.innerHTML = `<h3>❌ Error de Conexión</h3><p>${error.message}</p>`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        // Función para convertir MPD (sección avanzada)
        async function convertMpd() {
            const mpdUrl = document.getElementById('mpd-url').value.trim();
            if (!mpdUrl) {
                alert('Por favor ingresa una URL MPD');
                return;
            }

            document.getElementById('mpd-loading').style.display = 'block';
            document.getElementById('mpd-result').style.display = 'none';

            try {
                const response = await fetch('index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'url=' + encodeURIComponent(mpdUrl)
                });

                const data = await response.json();

                document.getElementById('mpd-loading').style.display = 'none';
                const resultDiv = document.getElementById('mpd-result');

                if (data.success) {
                    const result = `
                        <h4>✅ Conversión MPD Exitosa</h4>
                        <p><strong>Stream ID:</strong> ${data.data.stream_id}</p>
                        <p><strong>Canal:</strong> ${data.data.channel || 'N/A'}</p>
                        <p><strong>Outlet:</strong> ${data.data.outlet || 'N/A'}</p>
                        <p><strong>Idioma:</strong> ${data.data.language || 'N/A'}</p>
                        <p><strong>🎯 URL M3U8 Final:</strong></p>
                        <div class="final-url">${data.data.final_m3u8}</div>
                    `;
                    resultDiv.innerHTML = result;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<h4>❌ Error</h4><p>${data.data}</p>`;
                    resultDiv.className = 'result error';
                }

                resultDiv.style.display = 'block';
            } catch (error) {
                document.getElementById('mpd-loading').style.display = 'none';
                const resultDiv = document.getElementById('mpd-result');
                resultDiv.innerHTML = `<h4>❌ Error de Conexión</h4><p>${error.message}</p>`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        // Funciones de utilidad
        function loadEventExample() {
            document.getElementById('event-url').value = 'https://www.dazn.com/es-PA/fixture/ContentId:fiulkoqk5cdoaj20yjmcdeea/fiulkoqk5cdoaj20yjmcdeea/1ylmtt92k2xk6gay0ovzlrbbg';
            updateUrlType();
        }

        function loadMpdExample() {
            document.getElementById('mpd-url').value = 'https://dch-ac-live.cdn.indazn.com/1ks3vz4xf76pe14jyqg85ou3tv/tv25f/stream.mpd?channel=1637&outlet=dazn-row&plang=es&dazn-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InV1aWRfMSJ9.eyJwYXRocyI6WyIvMWtzM3Z6NHhmNzZwZTE0anlxZzg1b3UzdHYiXSwiZXhjIjpbXSwiaGVhZGVycyI6W10sImNvIjpmYWxzZSwiaXAiOmZhbHNlLCJhc24iOmZhbHNlLCJpbnRzaWciOiJ2d2N0U2tZZzk2RXY1enoyZEJwVVBOUk5HdHVpa2V4VWRVcWRzOFBSeDU4IiwiaWF0IjoxNzUwMTc1NzQ4LCJleHAiOjE3NTAyNjIxNDh9.P3Wk4sbLJnnBeflB-0V6LzWzyllr2K09ccInIbGepCI';
        }

        function clearMain() {
            document.getElementById('event-url').value = '';
            document.getElementById('main-result').style.display = 'none';
            document.getElementById('main-loading').style.display = 'none';
            document.getElementById('url-type').innerHTML = '';
        }
    </script>
</body>
</html>
