# DAZN Stream Extractor

Script PHP educativo para demostrar la extracción de IDs de streams y conversión de URLs MPD a M3U8.

⚠️ **IMPORTANTE**: Este script es solo para fines educativos y de demostración técnica. Respetar los términos de servicio de DAZN.

## 🚀 Características

- ✅ Extracción de Stream IDs de URLs MPD
- ✅ Conversión automática de MPD a M3U8
- ✅ Interfaz web amigable
- ✅ Login automático en DAZN
- ✅ Listado de eventos disponibles
- ✅ Optimizado para Hostinger

## 📋 Requisitos

- PHP 7.4 o superior
- Extensión cURL habilitada
- Permisos de escritura (para cookies)

## 🛠️ Instalación en Hostinger

### Paso 1: Subir archivos
1. Accede al panel de control de Hostinger
2. Ve a "Administrador de archivos"
3. Navega a la carpeta `public_html`
4. Sube los archivos:
   - `index.php`
   - `dazn_stream_extractor.php`

### Paso 2: Configurar permisos
```bash
chmod 755 index.php
chmod 755 dazn_stream_extractor.php
chmod 777 . # Para permitir creación de cookies.txt
```

### Paso 3: Verificar PHP
- Asegúrate de que cURL esté habilitado
- Verifica que la versión de PHP sea 7.4+

## 🎯 Uso

### Interfaz Web
1. Accede a tu dominio: `https://tudominio.com`
2. Usa las diferentes secciones:
   - **Convertir MPD a M3U8**: Pega una URL MPD y obtén la M3U8
   - **Login y Eventos**: Prueba la conexión y lista eventos
   - **Obtener Stream**: Extrae streams de eventos específicos

### Línea de comandos
```bash
php dazn_stream_extractor.php
```

### API Endpoints
```
GET /?action=convert&mpd_url=URL_MPD
GET /?action=login
GET /?action=events
GET /?action=stream&event_id=EVENT_ID
```

## 📝 Ejemplos

### Convertir URL MPD a M3U8
```php
$extractor = new DaznStreamExtractor($email, $password);

$mpdUrl = 'https://dcd-ak-livedazn.akamaized.net/1ks3vz4xf76pe14jyqg85ou3tv/tv25f/stream.mpd';
$streamId = $extractor->extractStreamId($mpdUrl);
$m3u8Url = $extractor->convertMpdToM3u8($mpdUrl);

echo "Stream ID: " . $streamId;
echo "M3U8 URL: " . $m3u8Url;
```

### Obtener eventos
```php
if ($extractor->login()) {
    $events = $extractor->getEvents();
    foreach ($events as $event) {
        echo $event['title'] . " - " . $event['id'];
    }
}
```

## 🔧 Configuración

### Credenciales
Edita las credenciales en `index.php`:
```php
$config = [
    'email' => '<EMAIL>',
    'password' => 'tu-password',
    'debug' => true
];
```

### Headers personalizados
Modifica los headers en `dazn_stream_extractor.php` si es necesario:
```php
private $headers = [
    'User-Agent: Mozilla/5.0...',
    'Accept: application/json...',
    // Agregar más headers aquí
];
```

## 🐛 Solución de problemas

### Error de cURL
```bash
# Verificar si cURL está instalado
php -m | grep curl
```

### Permisos de archivos
```bash
# Dar permisos de escritura para cookies
chmod 777 /ruta/a/tu/directorio
```

### Logs de errores
Habilita el debug en `index.php`:
```php
$config['debug'] = true;
```

## 📊 Estructura del proyecto

```
DAZN/
├── index.php                 # Interfaz web principal
├── dazn_stream_extractor.php # Clase principal
├── README.md                 # Este archivo
└── cookies.txt              # Archivo de cookies (se crea automáticamente)
```

## 🔒 Consideraciones de seguridad

1. **No hardcodear credenciales** en producción
2. **Usar HTTPS** siempre que sea posible
3. **Validar todas las entradas** del usuario
4. **Implementar rate limiting** para evitar abuso
5. **Rotar credenciales** regularmente

## 📚 Funciones principales

### `extractStreamId($mpdUrl)`
Extrae el ID único del stream de una URL MPD.

### `convertMpdToM3u8($mpdUrl)`
Convierte una URL MPD al formato M3U8 equivalente.

### `login()`
Realiza autenticación en DAZN y obtiene token de acceso.

### `getEvents()`
Obtiene lista de eventos disponibles.

### `getStreamUrl($eventId)`
Extrae URLs de stream para un evento específico.

## ⚖️ Disclaimer

Este script es únicamente para fines educativos y de demostración técnica. El uso de este código debe cumplir con:

- Términos de servicio de DAZN
- Leyes de derechos de autor aplicables
- Regulaciones locales sobre streaming

El autor no se hace responsable del uso indebido de este código.

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:

1. Fork el proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📄 Licencia

Este proyecto es solo para fines educativos. Usar bajo tu propia responsabilidad.
